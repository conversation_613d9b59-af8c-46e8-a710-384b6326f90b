/**
 * Isolated Test for Examples 5 and 6 - Prepend/Append Functionality
 * 
 * Tests the .prepend() and .append() methods in isolation
 */

import { FFMPEGVideoFilterModel } from './src/media/models/FFMPEGVideoFilterModel';
import { FFMPEGAPIClient } from './src/media/clients/FFMPEGAPIClient';
import { Video } from './src/media/assets/roles';
import * as fs from 'fs';
import * as path from 'path';

async function testExamples5And6() {
  console.log('🎬 Testing Examples 5 and 6 - Prepend/Append Functionality');
  console.log('============================================================');

  // Initialize the API client
  const apiClient = new FFMPEGAPIClient({
    baseUrl: 'http://localhost:8006'
  });

  // Create the fluent filter model
  const filter = new FFMPEGVideoFilterModel(undefined, apiClient);

  // Load sample videos
  const baseVideo = Video.fromFile('./test-videos/base.mp4');
  const overlay1 = Video.fromFile('./test-videos/overlay1.webm');
  const overlay2 = Video.fromFile('./test-videos/overlay2.webm');
  const overlay3 = Video.fromFile('./test-videos/overlay3.webm');
    // Load intro and outro videos for prepend/append examples
  const introVideo = Video.fromFile('./test-videos/intro.mp4'); // Using dedicated intro video
  const outroVideo = Video.fromFile('./test-videos/outro.mp4'); // Using dedicated outro video

  console.log('\n📹 Loaded test videos including intro/outro');
  // Example 5: Video with Intro and Outro (Prepend/Append)
  console.log('\n🎯 Example 5: Video with Intro and Outro');
  console.log('Filter: intro.mp4 -> base.mp4 + overlay -> outro.mp4');
  
  try {
    const introOutroResult = await filter
      .reset()
      .prepend(introVideo)
      .compose(baseVideo)
      .overlay(overlay1, {
        position: 'top-right',
        width: '25%',
        height: '25%',
        opacity: 0.8,
        startTime: 2,
        duration: 10
      })
      .append(outroVideo)
      .options({
        outputFormat: 'mp4',
        codec: 'libx264'
      })
      .transform();    console.log('✅ Intro/Outro composition completed:', introOutroResult.length, 'bytes');
    
    // Write the result to disk
    const outputPath5 = path.join(__dirname, 'example-5-intro-outro-result.mp4');
    fs.writeFileSync(outputPath5, introOutroResult);
    console.log('💾 Saved Example 5 result to:', outputPath5);
    
    // Preview the generated filter complex
    filter.reset();
    const introOutroFilter = filter
      .prepend(introVideo)
      .compose(baseVideo)
      .overlay(overlay1, {
        position: 'top-right',
        width: '25%',
        height: '25%',
        opacity: 0.8,
        startTime: 2
      })
      .append(outroVideo)
      .preview();
      
    console.log('🔍 Generated concatenation filter complex:');
    console.log(introOutroFilter);
    
  } catch (error) {
    console.error('❌ Intro/Outro composition failed:', error.message);
    console.error('Full error:', error);
  }

  // Example 6: Multiple Intros and Outros
  console.log('\n🎯 Example 6: Multiple Intros and Outros');
  console.log('Filter: intro1.prepend(intro2).compose(base).append(outro1, outro2)');
  
  try {
    const multiIntroOutroResult = await filter
      .reset()
      .prepend(introVideo, overlay2) // Multiple intro videos
      .compose(baseVideo)
      .overlay(overlay1, {
        position: 'center',
        width: '30%',
        height: '30%',
        opacity: 0.7,
        startTime: 3,
        duration: 8
      })
      .append(overlay3, outroVideo) // Multiple outro videos
      .options({
        outputFormat: 'mp4',
        codec: 'libx264'
      })
      .transform();    console.log('✅ Multi intro/outro composition completed:', multiIntroOutroResult.length, 'bytes');
    
    // Write the result to disk
    const outputPath6 = path.join(__dirname, 'example-6-multi-intro-outro-result.mp4');
    fs.writeFileSync(outputPath6, multiIntroOutroResult);
    console.log('💾 Saved Example 6 result to:', outputPath6);
    
    // Preview the generated filter complex
    filter.reset();
    const multiIntroOutroFilter = filter
      .prepend(introVideo, overlay2)
      .compose(baseVideo)
      .overlay(overlay1, {
        position: 'center',
        width: '30%',
        height: '30%',
        opacity: 0.7,
        startTime: 3
      })
      .append(overlay3, outroVideo)
      .preview();
      
    console.log('🔍 Generated multi intro/outro filter complex:');
    console.log(multiIntroOutroFilter);
    
  } catch (error) {
    console.error('❌ Multi intro/outro composition failed:', error.message);
    console.error('Full error:', error);
  }

  console.log('\n🎉 Examples 5 and 6 test completed!');
  console.log('\n📝 Features Tested:');
  console.log('✅ Single intro/outro with .prepend() and .append()');
  console.log('✅ Multiple intros/outros with multiple arguments');
  console.log('✅ Video concatenation with proper audio handling');
  console.log('✅ Filter complex generation for concatenation');
}

// Run the test
if (require.main === module) {
  testExamples5And6().catch(console.error);
}

export { testExamples5And6 };
